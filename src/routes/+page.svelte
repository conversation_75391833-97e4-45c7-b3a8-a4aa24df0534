<script lang="ts">
	import { PUBLIC_API_KEY, PUBLIC_API_URL } from '$env/static/public';
	import { onMount } from 'svelte';
	import { fade,crossfade } from 'svelte/transition';

	type Video = {
		imageId: string;
	};

	type ImageDetail = {
		id: string;
		likeCount: number;
		authorName: string;
		siteName: string;
		personaKey: string;
		visionStatementTitle: string;
		visionStatement: string;
		comments: Comment[];
		imageUrl: string;
		videoUrl: string;
	};

	type Comment = {
		personaKey: string;
		persona: string;
		comment: string;
	};

	let imageList = $state<ImageDetail[]>([]);
	let currentImageIndex = $state(0);
	let currentView = $state<
		| 'screensaver-1'
		| 'screensaver-2'
		| 'screensaver-3'
		| 'screensaver-4'
		| 'screensaver-5'
		| 'screensaver-6'
	>('screensaver-1');

	async function getVideo() {
		const response = await fetch(`${PUBLIC_API_URL}/page-content/assets/video?count=5`, {
			headers: { 'x-api-key': PUBLIC_API_KEY }
		});
		const {
			status,
			error,
			data: videos
		}: { status: string; error: string | null; data: Video[] } = await response.json();

		if (status === 'success') {
			const response = await Promise.all(
				videos.map(async (image) => {
					const response = await fetch(`${PUBLIC_API_URL}/page-content/asset/${image.imageId}`, {
						headers: {
							'x-api-key': PUBLIC_API_KEY
						}
					});
					const {
						status,
						error,
						data: imageDetail
					}: { status: string; error: string | null; data: ImageDetail } = await response.json();

					if (status === 'success') {
						// prefetch video url
						if (imageDetail.videoUrl) {
							const video = document.createElement('video');
							video.src = imageDetail.videoUrl;
						}

						return imageDetail;
					} else {
						console.error(error);
						return null;
					}
				})
			);

			return response.filter((image) => image !== null) as ImageDetail[];
		} else {
			console.error(error);
		}
	}

	onMount(() => {
		getVideo().then((images) => {
			if (images) {
				imageList = images.map((image) => {
					return {
						...image,
						visionStatementTitle: image.visionStatementTitle?.replace(
							/\*\*(.+?)\*\*/g,
							'<strong>$1</strong>'
						),
						visionStatement: image.visionStatement?.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
					};
				});
			}
		});
	});
</script>

{#if currentView.includes('screensaver')}
	<img width="205" src="/logo.png" class="absolute top-20 left-[110px] z-50" alt="dsa" />
{/if}

{#if currentView === 'screensaver-1'}
	<div in:fade={{  duration: 800 }} out:fade={{ duration: 800 }} class="blend-container">
		<video class="opacity-30" src="/screensaver-prism.webm" autoplay muted loop></video>
		<video
			src={imageList?.[currentImageIndex]?.videoUrl}
			class="blend-image"
			id="zoom-image"
			autoplay
			muted
			onended={() => {
				currentImageIndex++;
				currentView = 'screensaver-2';
			}}
		></video>

		<div
			class="absolute bottom-[110px] left-[110px] z-50 flex flex-col items-start justify-start gap-y-16"
		>
			<div class="">
				<p
					class="font-syncopate mb-11 max-w-[1200px] text-[80px] leading-[80px] font-bold text-white"
				>
					reimagine <br /> our city together
				</p>

				<p
					class="font-metropolis flex w-fit items-center justify-center gap-4 rounded-full bg-[#191919] px-6 py-2 text-[20px] text-white"
				>
					<span class="font-normal">
						<span class="font-medium">Reimagine {imageList?.[currentImageIndex]?.siteName}</span> |
						By {imageList?.[currentImageIndex]?.authorName}
					</span>

					<svg
						width="37"
						height="15"
						viewBox="0 0 37 15"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
					>
						<path
							d="M35 7.58282L1 7.58281M29.7001 1.55835L35.7501 7.58235L29.7001 13.6073"
							stroke="white"
							stroke-width="1.5"
							stroke-linecap="square"
						/>
					</svg>
				</p>
			</div>

			<div class="font-metropolis w-140 rounded-xl bg-[#60606080] p-6 text-white">
				<p class="mb-7 text-[36px] font-bold">
					{imageList?.[currentImageIndex]?.visionStatementTitle}
				</p>
				<p class="text-[18px] leading-[24px]">
					{@html imageList?.[currentImageIndex]?.visionStatement}
				</p>
			</div>
		</div>
	</div>
{:else if currentView === 'screensaver-2'}
	<div in:fade={{  duration: 800 }} out:fade={{ duration: 800 }} class="blend-container">
		<video class="opacity-30" src="/screensaver-prism.webm" autoplay muted loop></video>
		<video
			src={imageList?.[currentImageIndex]?.videoUrl}
			class="blend-image"
			id="zoom-image"
			autoplay
			muted
			onended={() => {
				currentImageIndex++;
				currentView = 'screensaver-3';
			}}
		></video>

		<div
			class="absolute bottom-[110px] left-[110px] z-50 flex flex-col items-start justify-start gap-y-16"
		>
			<div class="">
				<p
					class="font-syncopate mb-11 max-w-[1200px] text-[80px] leading-[80px] font-bold text-white"
				>
					reimagine <br /> our city together
				</p>

				<p
					class="font-metropolis flex w-fit items-center justify-center gap-4 rounded-full bg-[#191919] px-6 py-2 text-[20px] text-white"
				>
					<span class="font-normal">
						<span class="font-medium">Reimagine {imageList?.[currentImageIndex]?.siteName}</span> |
						By {imageList?.[currentImageIndex]?.authorName}
					</span>

					<svg
						width="37"
						height="15"
						viewBox="0 0 37 15"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
					>
						<path
							d="M35 7.58282L1 7.58281M29.7001 1.55835L35.7501 7.58235L29.7001 13.6073"
							stroke="white"
							stroke-width="1.5"
							stroke-linecap="square"
						/>
					</svg>
				</p>
			</div>

			<div class="font-metropolis w-140 rounded-xl bg-[#60606080] p-6 text-white">
				<p class="mb-7 text-[36px] font-bold">
					{imageList?.[currentImageIndex]?.visionStatementTitle}
				</p>
				<p class="text-[18px] leading-[24px]">
					{@html imageList?.[currentImageIndex]?.visionStatement}
				</p>
			</div>
		</div>
	</div>
{:else if currentView === 'screensaver-3'}
	<div in:fade={{  duration: 800 }} out:fade={{ duration: 800 }} class="blend-container">
		<video class="opacity-30" src="/screensaver-prism.webm" autoplay muted loop></video>
		<video
			src={imageList?.[currentImageIndex]?.videoUrl}
			class="blend-image"
			id="zoom-image"
			autoplay
			muted
			onended={() => {
				currentImageIndex++;
				currentView = 'screensaver-4';
			}}
		></video>

		<div
			class="absolute bottom-[110px] left-[110px] z-50 flex flex-col items-start justify-start gap-y-16"
		>
			<div class="">
				<p
					class="font-syncopate mb-11 max-w-[1200px] text-[80px] leading-[80px] font-bold text-white"
				>
					reimagine <br /> our city together
				</p>

				<p
					class="font-metropolis flex w-fit items-center justify-center gap-4 rounded-full bg-[#191919] px-6 py-2 text-[20px] text-white"
				>
					<span class="font-normal">
						<span class="font-medium">Reimagine {imageList?.[currentImageIndex]?.siteName}</span> |
						By {imageList?.[currentImageIndex]?.authorName}
					</span>

					<svg
						width="37"
						height="15"
						viewBox="0 0 37 15"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
					>
						<path
							d="M35 7.58282L1 7.58281M29.7001 1.55835L35.7501 7.58235L29.7001 13.6073"
							stroke="white"
							stroke-width="1.5"
							stroke-linecap="square"
						/>
					</svg>
				</p>
			</div>

			<div class="font-metropolis w-140 rounded-xl bg-[#60606080] p-6 text-white">
				<p class="mb-7 text-[36px] font-bold">
					{imageList?.[currentImageIndex]?.visionStatementTitle}
				</p>
				<p class="text-[18px] leading-[24px]">
					{@html imageList?.[currentImageIndex]?.visionStatement}
				</p>
			</div>
		</div>
	</div>
{:else if currentView === 'screensaver-4'}
	<div in:fade={{  duration: 800 }} out:fade={{ duration: 800 }} class="blend-container">
		<video class="opacity-30" src="/screensaver-prism.webm" autoplay muted loop></video>
		<video
			src={imageList?.[currentImageIndex]?.videoUrl}
			class="blend-image"
			id="zoom-image"
			autoplay
			muted
			onended={() => {
				currentImageIndex++;
				currentView = 'screensaver-5';
			}}
		></video>

		<div
			class="absolute bottom-[110px] left-[110px] z-50 flex flex-col items-start justify-start gap-y-16"
		>
			<div class="">
				<p
					class="font-syncopate mb-11 max-w-[1200px] text-[80px] leading-[80px] font-bold text-white"
				>
					reimagine <br /> our city together
				</p>

				<p
					class="font-metropolis flex w-fit items-center justify-center gap-4 rounded-full bg-[#191919] px-6 py-2 text-[20px] text-white"
				>
					<span class="font-normal">
						<span class="font-medium">Reimagine {imageList?.[currentImageIndex]?.siteName}</span> |
						By {imageList?.[currentImageIndex]?.authorName}
					</span>

					<svg
						width="37"
						height="15"
						viewBox="0 0 37 15"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
					>
						<path
							d="M35 7.58282L1 7.58281M29.7001 1.55835L35.7501 7.58235L29.7001 13.6073"
							stroke="white"
							stroke-width="1.5"
							stroke-linecap="square"
						/>
					</svg>
				</p>
			</div>

			<div class="font-metropolis w-140 rounded-xl bg-[#60606080] p-6 text-white">
				<p class="mb-7 text-[36px] font-bold">
					{imageList?.[currentImageIndex]?.visionStatementTitle}
				</p>
				<p class="text-[18px] leading-[24px]">
					{@html imageList?.[currentImageIndex]?.visionStatement}
				</p>
			</div>
		</div>
	</div>
{:else if currentView === 'screensaver-5'}
	<div in:fade={{  duration: 800 }} out:fade={{ duration: 800 }} class="blend-container">
		<video class="opacity-30" src="/screensaver-prism.webm" autoplay muted loop></video>
		<video
			src={imageList?.[currentImageIndex]?.videoUrl}
			class="blend-image"
			id="zoom-image"
			autoplay
			muted
			onended={() => {
				currentImageIndex++;
				currentView = 'screensaver-6';
			}}
		></video>

		<div
			class="absolute bottom-[110px] left-[110px] z-50 flex flex-col items-start justify-start gap-y-16"
		>
			<div class="">
				<p
					class="font-syncopate mb-11 max-w-[1200px] text-[80px] leading-[80px] font-bold text-white"
				>
					reimagine <br /> our city together
				</p>

				<p
					class="font-metropolis flex w-fit items-center justify-center gap-4 rounded-full bg-[#191919] px-6 py-2 text-[20px] text-white"
				>
					<span class="font-normal">
						<span class="font-medium">Reimagine {imageList?.[currentImageIndex]?.siteName}</span> |
						By {imageList?.[currentImageIndex]?.authorName}
					</span>

					<svg
						width="37"
						height="15"
						viewBox="0 0 37 15"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
					>
						<path
							d="M35 7.58282L1 7.58281M29.7001 1.55835L35.7501 7.58235L29.7001 13.6073"
							stroke="white"
							stroke-width="1.5"
							stroke-linecap="square"
						/>
					</svg>
				</p>
			</div>

			<div class="font-metropolis w-140 rounded-xl bg-[#60606080] p-6 text-white">
				<p class="mb-7 text-[36px] font-bold">
					{imageList?.[currentImageIndex]?.visionStatementTitle}
				</p>
				<p class="text-[18px] leading-[24px]">
					{@html imageList?.[currentImageIndex]?.visionStatement}
				</p>
			</div>
		</div>
	</div>
{:else if currentView === 'screensaver-6'}
	<video
		in:fade={{  duration: 800 }}
		out:fade={{ duration: 800 }}
		src="/led.webm"
		autoplay
		muted
		onloadedmetadata={() => {
			getVideo().then((images) => {
				if (images) {
					imageList = images.map((image) => {
						return {
							...image,
							visionStatementTitle: image.visionStatementTitle?.replace(
								/\*\*(.+?)\*\*/g,
								'<strong>$1</strong>'
							),
							visionStatement: image.visionStatement?.replace(
								/\*\*(.+?)\*\*/g,
								'<strong>$1</strong>'
							)
						};
					});
				}
			});
		}}
		onended={() => {
			currentImageIndex = 0;
			currentView = 'screensaver-1';
		}}
	></video>
{/if}

<style>
	.blend-container {
		position: relative;
		width: 100%;
		height: 100%;
		overflow: hidden;
	}

	.blend-container video {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.blend-image {
		mix-blend-mode: screen;
	}
</style>
